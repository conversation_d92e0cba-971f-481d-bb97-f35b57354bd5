# Native Video Cache - Flutter 视频缓存插件

Native Video Cache 是一个 Flutter 插件，为 iOS 和 Android 平台提供高效的视频缓存功能。它基于成熟的原生缓存库（iOS: KTVHTTPCache，Android: AndroidVideoCache），通过 Pigeon 生成统一的 Dart API。

## 📋 版本兼容性

| 组件 | 版本要求 |
|------|----------|
| **Flutter** | >= 3.3.0 |
| **Dart** | >= 2.17.0 < 4.0.0 |
| **iOS** | >= 10.0 |
| **Android** | >= API 21 (Android 5.0) |

### 依赖库版本
- **iOS**: KTVHTTPCache (内置)
- **Android**: AndroidVideoCache v2.7.1 (via JitPack)
- **Pigeon**: ^17.1.2


### Demo功能对比

| 功能 | VideoPlayerDemo | OptimizedCacheDemo | 优势 |
|------|----------------|-------------------|------|
| 视频播放 | ✅ | ✅ | 相同 |
| 缓存管理 | ✅ | ✅ | 更智能 |
| 错误处理 | ✅ | ✅ | 更详细 |
| 状态管理 | 基础 | 统一管理 | 更可靠 |
| 性能监控 | 无 | ✅ | 新增 |
| 配置管理 | 基础 | 分层配置 | 更灵活 |
| 依赖注入 | 无 | ✅ | 更可测试 |
| 多视频管理 | 无 | ✅ | 新增 |


## ✨ 核心特性

### 🎯 基础功能
- ✅ 支持 iOS 和 Android 平台
- ✅ 视频边下边播（流式缓存）
- ✅ 可配置的缓存大小和缓存策略
- ✅ 实时获取视频缓存状态和进度
- ✅ 支持预缓存（后台下载）
- ✅ 清理指定或所有缓存
- ✅ 支持HTTP和HTTPS视频源
- ✅ 多种视频格式支持（MP4、M3U8、HLS等）
- ✅ 线程安全的并发缓存管理

### 🛡️ 企业级功能 (v0.0.2+)
- ✅ **AppLifecycleManager**: APP生命周期管理，自动处理前后台切换
- ✅ **CacheMemoryManager**: LRU内存管理，防止内存泄漏
- ✅ **CacheStabilityManager**: 自动重试机制和异常恢复
- ✅ **CachePerformanceMonitor**: 性能监控和健康检查

### 🚀 最新优化 (v0.0.4)
- ✅ **依赖注入架构**: 解除硬编码依赖，提高可测试性
- ✅ **统一状态管理**: `CacheStateManager`统一管理所有缓存状态
- ✅ **智能错误处理**: 结构化错误分类和自动重试
- ✅ **分层配置系统**: 支持全局、性能、网络、安全四层配置
- ✅ **内存泄漏修复**: 完全解决资源清理问题
- ✅ **OptimizedCacheDemo**: 优化的演示应用，可完全替代VideoPlayerDemo

## 🚀 快速开始

### 第一步：添加依赖

在 `pubspec.yaml` 文件中添加插件依赖：

```yaml
dependencies:
  native_video_cache:
    git:
      url: https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
      path: packages/native_video_cache
```

然后运行：
```bash
flutter pub get
```

### 第二步：Android 配置

#### 1. 添加网络权限
在 `android/app/src/main/AndroidManifest.xml` 中添加：

```xml
<manifest ...>
    <application
        ...
        android:usesCleartextTraffic="true">
        ...
    </application>
</manifest>
```

#### 2. 添加Maven仓库
在 `android/build.gradle` 中添加：

```gradle
allprojects {
    repositories {
        maven { url "https://jitpack.io" }
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
    }
}
```

在 `android/settings.gradle` 中添加：

```gradle
pluginManagement {
    repositories {
        maven { url "https://jitpack.io" }
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
```

### 第三步：初始化并使用

#### 基础使用
```dart
import 'package:native_video_cache/native_video_cache.dart';

// 1. 初始化
await NativeVideoCacheManager.initialize(
  CacheConfig(maxCacheSize: 1024 * 1024 * 1024), // 1GB
);

// 2. 获取代理URL用于播放器
String proxyUrl = await NativeVideoCacheManager.getProxyUrl(originalUrl);

// 3. 用于视频播放器
VideoPlayerController.network(proxyUrl);
```

#### v0.0.4 优化用法
```dart
// 使用优化的演示应用（完全替代VideoPlayerDemo）
OptimizedCacheDemo(
  videoUrl: 'https://example.com/video.mp4',
  title: '我的视频',
)

// 或多视频管理模式
OptimizedCacheDemo() // 不传参数进入管理模式
```

#### 企业级配置 (v0.0.4)
```dart
// 1. 配置系统
const configuration = CacheConfiguration(
  global: GlobalConfig(
    maxCacheSize: 2 * 1024 * 1024 * 1024, // 2GB
    enableDebugLogging: true,
  ),
  performance: PerformanceConfig(
    maxConcurrentDownloads: 5,
    maxActiveListeners: 100,
  ),
  network: NetworkConfig(
    maxRetryAttempts: 3,
    enableBackgroundCaching: true,
  ),
);

// 2. 初始化
CacheConfigurationManager.instance.initialize(configuration);
CacheStateManager.instance.initialize();
await NativeVideoCacheManager.initialize(
  CacheConfig(maxCacheSize: configuration.global.maxCacheSize),
);
```

## 📖 详细使用指南

### 缓存管理
```dart
// 主动开始缓存
await NativeVideoCacheManager.startCache(originalUrl);

// 停止缓存
await NativeVideoCacheManager.stopCache(originalUrl);

// 检查是否已缓存
bool isCached = await NativeVideoCacheManager.isCached(originalUrl);

// 获取缓存信息
CacheInfo info = await NativeVideoCacheManager.getCacheInfo(originalUrl);
```

### 监听缓存状态
```dart
// 监听进度
NativeVideoCacheManager.addProgressListener((url, progress) {
  print('缓存进度: $url -> ${(progress * 100).toStringAsFixed(1)}%');
});

// 监听状态变化
NativeVideoCacheManager.addStatusListener((url, status) {
  print('缓存状态: $url -> $status');
});

// 监听错误
NativeVideoCacheManager.addErrorListener((url, error) {
  print('缓存错误: $url -> $error');
});

// 重要：清理监听器
@override
void dispose() {
  NativeVideoCacheManager.clearAllListeners();
  super.dispose();
}
```

### 缓存清理
```dart
// 清理指定URL的缓存
await NativeVideoCacheManager.clearCache(originalUrl);

// 清理所有缓存
await NativeVideoCacheManager.clearAllCache();

// 获取总缓存大小
int totalSize = await NativeVideoCacheManager.getTotalCacheSize();
```

## 🏗️ 底层实现

### iOS - KTVHTTPCache
- 通过本地HTTP代理服务器工作
- 提供精确的字节级进度追踪
- 支持预加载和断点续传
- 成熟稳定，广泛应用于大型APP

### Android - AndroidVideoCache
- 同样使用本地HTTP代理机制
- 轻量级且易于集成
- **注意**: 缓存进度为估算值，可能不如iOS精确
- 缓存是"惰性"的，在播放器请求时才开始

### 统一接口层 - Pigeon
使用 Google Pigeon 工具生成类型安全的平台绑定代码，确保：
- Dart API 在两个平台上表现一致
- 类型安全的跨平台通信
- 统一的错误处理机制

## 🛠️ 高级功能

### 企业级管理器组合 (v0.0.2+)

```dart
import 'package:native_video_cache/native_video_cache.dart';

// 初始化所有增强管理器
Future<void> initializeEnterpriseFeatures() async {
  // 1. 核心缓存系统
  await NativeVideoCacheManager.initialize(
    CacheConfig(maxCacheSize: 2048 * 1024 * 1024),
  );

  // 2. 生命周期管理
  AppLifecycleManager.instance.initialize();

  // 3. 内存管理
  CacheMemoryManager.instance.initialize(
    maxUrls: 200,
    maxMemoryUsage: 100 * 1024 * 1024,
  );

  // 4. 稳定性管理
  await CacheStabilityManager.instance.initialize(
    maxRetries: 5,
    retryDelay: Duration(seconds: 3),
  );

  // 5. 性能监控
  CachePerformanceMonitor.instance.initialize();
}

// 获取综合报告
void generateReport() {
  final performanceReport = CachePerformanceMonitor.instance.getPerformanceReport();
  final memoryReport = CacheMemoryManager.instance.getMemoryReport();
  final healthReport = CacheStabilityManager.instance.getHealthReport();
  
  print('成功率: ${(performanceReport['overallSuccessRate'] * 100).toStringAsFixed(1)}%');
  print('内存使用: ${memoryReport['memoryUsage']} bytes');
  print('重试次数: ${healthReport['retriedTasks']}');
}
```

## 📊 性能特征

### 缓存效率
- **iOS**: 精确的字节级进度追踪
- **Android**: 高效的代理缓存机制，进度为估算值
- **内存占用**: 低内存占用，主要缓存存储在磁盘
- **网络优化**: 支持断点续传和分片下载

### 并发支持
- 默认支持最多3个并发下载任务
- 可通过配置调整并发数量
- 智能队列管理，避免网络拥塞

## 🔧 最佳实践

### 🧠 智能缓存策略详解

Native Video Cache 采用**四层智能缓存架构**，提供企业级的缓存管理能力：

#### 1. 基础缓存策略层
```dart
final config = CacheConfig(
  maxCacheSize: 2 * 1024 * 1024 * 1024, // 2GB 智能空间管理
  maxConcurrentDownloads: 3,             // 并发控制，防止带宽拥堵
  connectTimeout: 15 * 1000,             // 15秒 连接超时
  readTimeout: 30 * 1000,                // 30秒 读取超时
);

await NativeVideoCacheManager.initialize(config);
```

**核心特性**：
- ✅ **边下边播**: 流式缓存，无需等待完整下载
- ✅ **透明代理**: 播放器无感知，自动缓存
- ✅ **多格式支持**: MP4、M3U8、HLS等智能识别
- ✅ **带宽优化**: 智能并发控制，避免网络拥塞

#### 2. APP生命周期智能管理
```dart
// 自动管理缓存的生命周期行为
AppLifecycleManager.instance.initialize();

// 配置选项
AppLifecycleManager.instance.enableBackgroundCaching = true;
AppLifecycleManager.instance.backgroundTimeout = Duration(minutes: 5);
```

**智能策略**：
- 🔋 **前台模式**: 全速缓存，最佳用户体验
- 🔋 **后台模式**: 智能降速，节省电量和流量  
- ⏰ **超时保护**: 后台5分钟后自动暂停缓存
- 🚀 **快速恢复**: 前台切换时毫秒级恢复所有缓存任务

#### 3. LRU内存智能管理
```dart
// 智能内存管理，防止内存泄漏
CacheMemoryManager.instance.initialize(
  maxUrls: 100,                    // 最大缓存状态数
  maxMemoryUsage: 50 * 1024 * 1024, // 50MB内存限制
);
```

**LRU策略**：
- 📊 **自动淘汰**: 最近最少使用的缓存状态自动清理
- 🚨 **压力检测**: 内存使用过高时主动清理
- ⏰ **定时维护**: 每2分钟清理过期监听器
- 🎯 **精准控制**: 智能限制活跃监听器数量

#### 4. 智能重试与稳定性管理
```dart
// 自动重试机制，提升缓存成功率
await CacheStabilityManager.instance.initialize(
  maxRetries: 3,                        // 最大重试次数
  retryDelay: Duration(seconds: 30),    // 重试间隔
);
```

**重试策略**：
- 🔄 **指数退避**: 30秒→60秒→120秒递增延迟
- 🎯 **智能重试**: 只重试有希望恢复的任务
- 📊 **自动放弃**: 3次失败后停止，避免资源浪费
- 🔍 **故障分析**: 记录失败原因，便于问题诊断

#### 5. 性能智能监控
```dart
// 实时性能监控和优化建议
CachePerformanceMonitor.instance.initialize();

// 获取性能报告
final report = CachePerformanceMonitor.instance.getPerformanceReport();
print('缓存成功率: ${(report['overallSuccessRate'] * 100).toStringAsFixed(1)}%');
print('平均下载速度: ${report['averageSpeed']} KB/s');
```

**监控特性**：
- 📈 **实时统计**: 下载速度、成功率、错误分析
- 🎯 **智能调优**: 根据网络状况动态调整策略
- 📝 **性能报告**: 每分钟生成详细分析报告
- 🔍 **异常检测**: 提前发现潜在的缓存问题

#### 6. 综合智能策略示例
```dart
// 完整的智能缓存初始化
Future<void> setupIntelligentCaching() async {
  // 1. 基础缓存配置
  await NativeVideoCacheManager.initialize(CacheConfig(
    maxCacheSize: 2 * 1024 * 1024 * 1024,  // 2GB
    maxConcurrentDownloads: 3,              // 适中并发
    connectTimeout: 15 * 1000,              // 15秒超时
    readTimeout: 30 * 1000,                 // 30秒读取
  ));

  // 2. 生命周期智能管理
  AppLifecycleManager.instance
    ..initialize()
    ..enableBackgroundCaching = true        // 允许后台缓存
    ..backgroundTimeout = Duration(minutes: 5); // 5分钟后台超时

  // 3. 内存智能管理
  CacheMemoryManager.instance.initialize(
    maxUrls: 200,                           // 200个URL状态
    maxMemoryUsage: 100 * 1024 * 1024,      // 100MB内存限制
  );

  // 4. 稳定性管理
  await CacheStabilityManager.instance.initialize(
    maxRetries: 5,                          // 5次重试
    retryDelay: Duration(seconds: 3),       // 3秒间隔
  );

  // 5. 性能监控
  CachePerformanceMonitor.instance.initialize();

  print('🎯 智能缓存系统初始化完成');
}

// 智能缓存使用
Future<void> playVideoWithIntelligentCache(String videoUrl) async {
  try {
    // 注册到生命周期管理
    AppLifecycleManager.instance.registerActiveUrl(videoUrl);
    
    // 注册到稳定性管理（自动重试）
    CacheStabilityManager.instance.registerCacheTask(videoUrl);
    
    // 获取代理URL（自动启用所有智能策略）
    final proxyUrl = await NativeVideoCacheManager.getProxyUrl(videoUrl);
    
    // 使用代理URL播放视频
    // videoPlayer.setUrl(proxyUrl);
    
    print('✅ 智能缓存已启用: $proxyUrl');
  } catch (e) {
    print('❌ 缓存启动失败: $e');
  }
}
```

#### 🏆 智能优势对比

| 特性 | 传统缓存 | Native Video Cache智能策略 |
|------|---------|---------------------------|
| **缓存模式** | 预加载全部 | ✅ **边下边播**，智能流式缓存 |
| **内存管理** | 手动释放 | ✅ **LRU自动**清理，内存压力检测 |
| **生命周期** | 无感知 | ✅ **智能省电**，后台自动降级 |
| **错误处理** | 用户重试 | ✅ **自动重试**，指数退避算法 |
| **性能监控** | 无监控 | ✅ **实时监控**，智能调优建议 |
| **资源控制** | 无限制 | ✅ **智能并发**控制，防止拥堵 |

这套四层智能架构确保了**高性能、低资源消耗、高稳定性**的视频缓存体验！

### 生命周期管理
```dart
class VideoPlayerPage extends StatefulWidget {
  @override
  _VideoPlayerPageState createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  @override
  void dispose() {
    // 清理监听器避免内存泄漏
    NativeVideoCacheManager.clearAllListeners();
    super.dispose();
  }
}
```

### 预缓存策略
```dart
// WiFi环境下预缓存
Future<void> preloadVideos(List<String> urls) async {
  for (String url in urls) {
    try {
      await NativeVideoCacheManager.startCache(url);
      await Future.delayed(Duration(seconds: 1)); // 避免网络拥塞
    } catch (e) {
      print('预缓存失败: $url');
    }
  }
}
```

## 🐛 故障排除

### 常见问题

#### Android构建失败
**问题**: 找不到AndroidVideoCache依赖  
**解决**: 确保添加了JitPack仓库到 `build.gradle` 和 `settings.gradle`

#### 缓存进度不准确
**问题**: Android端进度显示异常  
**解决**: 这是正常现象，Android端进度为估算值，建议以缓存状态为准

#### 内存泄漏
**问题**: 应用内存持续增长  
**解决**: 确保在Widget dispose时调用 `clearAllListeners()`

### 调试技巧

```dart
// 启用详细日志
if (kDebugMode) {
  NativeVideoCacheManager.addProgressListener((url, progress) {
    debugPrint('缓存进度: $url -> $progress');
  });
  
  NativeVideoCacheManager.addErrorListener((url, error) {
    debugPrint('缓存错误: $url -> $error');
  });
}
```

## 📈 版本更新

最新版本的详细更改请查看 [CHANGELOG.md](CHANGELOG.md)

### 主要版本里程碑
- **v0.0.1**: 初始版本，基础缓存功能
- **v0.0.2**: 添加企业级管理器（生命周期、内存、稳定性、性能监控）
- **v0.0.3**: 修复管理器问题，添加完整测试套件
- **v0.0.4**: 架构重构，依赖注入，统一状态管理，内存泄漏修复

## 📚 示例项目

完整的示例项目位于 `example/` 目录：

```bash
cd example
flutter run
```

示例包含：
- 基础缓存功能演示
- 视频播放器集成
- 缓存状态监听
- 批量预缓存
- 优化架构演示 (OptimizedCacheDemo)

## ⚠️ 注意事项

### 平台差异
- **Android**: 缓存进度为估算值，可能不够精确
- **iOS**: 提供精确的进度追踪
- **通用**: HLS直播流不支持缓存（仅支持点播）

### 性能限制
- 建议单个视频文件不超过2GB
- 并发下载数量建议不超过5个
- 在低性能设备上建议降低并发数量

### 网络要求
- 需要稳定的网络连接
- 支持HTTP/HTTPS协议
- 某些CDN可能有防盗链限制

## 🤝 贡献

欢迎社区贡献！

1. 提交Issue描述问题
2. Fork项目并创建特性分支
3. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 💡 支持

如果这个插件对你有帮助，请给我们一个⭐️！

有问题或建议？请在GitHub上提交Issue。
